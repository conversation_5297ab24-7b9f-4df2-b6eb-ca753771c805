# Enhanced Backdrop Blur System

## Overview
This document outlines the enhanced backdrop blur system implemented globally across the application to provide better UI/UX standards with darker backgrounds and stronger blur effects.

## Global Blur Utilities

### CSS Classes (globals.css)
```css
.backdrop-blur-enhanced {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.backdrop-blur-strong {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.backdrop-blur-modal {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-color: hsl(var(--semantic-gray-900) / 0.15);
}

.backdrop-blur-overlay {
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  background-color: hsl(var(--semantic-gray-900) / 0.12);
}

.backdrop-blur-drawer {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  background-color: hsl(var(--semantic-gray-900) / 0.08);
}
```

### Tailwind Utilities (tailwind.config.ts)
```javascript
backdropBlur: {
  'xs': '2px',
  'sm': '4px',
  'DEFAULT': '8px',
  'md': '12px',
  'lg': '16px',
  'xl': '24px',
  '2xl': '40px',
  '3xl': '64px',
  'enhanced': '8px',
  'strong': '12px',
  'modal': '8px',
  'overlay': '6px',
  'drawer': '4px'
}
```

## Implementation Guidelines

### Modal Components
- **Background opacity**: Increased from 0.03-0.05 to 0.15-0.20
- **Blur strength**: Increased from 1px-4px to 8px-12px
- **Usage**: Use `backdrop-blur-modal` class or equivalent inline styles

### Drawer/Overlay Components
- **Background opacity**: Increased from 0.05 to 0.12
- **Blur strength**: Increased from 4px to 6px
- **Usage**: Use `backdrop-blur-overlay` class

### Sticky Headers
- **Background opacity**: Semi-transparent (0.92-0.95)
- **Blur strength**: Strong blur (12px) for better content separation
- **Usage**: Use `backdrop-blur-strong` class

### Premium/Lock Overlays
- **Background opacity**: Increased to 0.15
- **Blur strength**: Extra strong (16px) for clear visual hierarchy
- **Usage**: Use inline styles with 16px blur

## Browser Compatibility
All blur effects include both `backdrop-filter` and `-webkit-backdrop-filter` for maximum browser compatibility.

## Performance Considerations
- Blur effects are optimized for modern browsers
- Fallback backgrounds are provided for browsers without backdrop-filter support
- CSS variables are used for consistent theming

## Usage Examples

### Modal Implementation
```jsx
<div className="fixed inset-0 backdrop-blur-modal flex justify-center items-center z-50">
  {/* Modal content */}
</div>
```

### Drawer Implementation
```jsx
<div className="fixed inset-0 backdrop-blur-overlay z-30">
  {/* Drawer content */}
</div>
```

### Sticky Header Implementation
```jsx
<div className="sticky top-0 backdrop-blur-strong z-10">
  {/* Header content */}
</div>
```

## Updated Components
The following components have been updated with enhanced blur effects:

1. **Modal Components**
   - FigmaExtractionModal
   - DeepQueryModal
   - DiscussionModal
   - ApprovalComments
   - RepositoryModal
   - DeleteConfirmationModal
   - ConfigureModel

2. **Overlay Components**
   - PremiumOverlay
   - LockedTab overlay
   - Drawer overlay
   - Sheet overlay

3. **Sticky Elements**
   - CollapseViewer header
   - Requirements tab header
   - Home page tab effects

## Benefits
- **Better Visual Hierarchy**: Stronger blur creates clearer separation between layers
- **Improved Readability**: Darker backgrounds provide better contrast
- **Professional Appearance**: Enhanced blur effects follow modern UI/UX standards
- **Consistent Experience**: Standardized blur values across all components
- **Accessibility**: Better contrast ratios for improved accessibility

## Maintenance
- All blur values are centralized in globals.css and tailwind.config.ts
- Use the predefined classes instead of inline styles when possible
- Update the documentation when adding new blur utilities

---

# Tab Styling Updates - Neutral Color Scheme

## Overview
All tab components have been updated to use neutral colors (white, gray, black) instead of orange/primary colors for a cleaner, more professional appearance.

## Updated Tab Components

### 1. Global Tab Styles (globals.css)
- **Active tabs**: Changed from orange primary to `semantic-gray-900` with `semantic-gray-700` borders
- **Inactive tabs**: Use `semantic-gray-600` with hover states in `semantic-gray-800`
- **Tab containers**: Maintain `semantic-gray-50` backgrounds with neutral borders

### 2. Main Tab Component (Tabs/css/tabs.css)
- **Active tab border**: Changed from `primary-500` to `semantic-gray-700`
- **Active tab text**: Changed from `primary` to `semantic-gray-900`
- **Close button hover**: Changed from `primary-100/300` to `semantic-gray-200/300`

### 3. Home Page Tabs (home.css)
- **Active tabs**: White background with `semantic-gray-900` text and subtle shadow
- **Inactive tabs**: Maintain gray backgrounds with improved hover states

### 4. Professional Tabs Component
- **Active state**: `gray-700` border, `gray-900` text, `gray-50` background
- **Inactive state**: `gray-600` text with `gray-800` hover and `gray-100` background hover
- **Dropdown items**: `gray-100` background for active, `gray-50` hover for inactive

### 5. Locked Tab Styling
- **Hover effects**: Changed from primary colors to `semantic-gray-300` borders
- **Overlay gradients**: Updated to use `semantic-gray-200` instead of primary colors

### 6. Deployment Page Tabs
- **Active tabs**: `semantic-gray-900` text with white backgrounds
- **Inactive tabs**: `semantic-gray-600` text with hover states

## Color Mapping
| Old Color | New Color | Usage |
|-----------|-----------|-------|
| `hsl(var(--primary))` | `hsl(var(--semantic-gray-900))` | Active tab text |
| `hsl(var(--primary-500))` | `hsl(var(--semantic-gray-700))` | Active tab borders |
| `hsl(var(--primary-600))` | `hsl(var(--semantic-gray-900))` | Active tab icons |
| `hsl(var(--primary-100))` | `hsl(var(--semantic-gray-200))` | Hover backgrounds |
| `hsl(var(--primary-50))` | `hsl(var(--semantic-gray-100))` | Light backgrounds |

## Benefits
- **Professional Appearance**: Clean, neutral design that doesn't compete with content
- **Better Accessibility**: Higher contrast ratios with gray-on-white combinations
- **Consistent Branding**: Aligns with user preference for minimal orange usage
- **Improved Focus**: Content takes precedence over interface elements
