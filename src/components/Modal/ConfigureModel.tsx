"use client";

import React, { useState, useEffect, useRef, useContext } from "react";
import { useParams } from "next/navigation";
import { X, Check, ChevronRight } from "lucide-react";
import { configureNodeWithAgent, deleteTask, getAvailableOptions } from "@/utils/api";
import { ExecutionContext } from "../Context/ExecutionContext";
import { AlertContext } from "../NotificationAlertService/AlertList";
import EitherOrModal from "./EitherOrModal";
import { DynamicButton } from "../UIComponents/Buttons/DynamicButton";
import { StateContext } from "../Context/StateContext";
import { ETAComponent } from "../ETA";
import { CONFIG_TIMING_BY_PATTERN, PatternType } from "@/constants/architecture/pattern";

interface ConfigOption {
  configure: boolean;
  [key: string]: ConfigOption | boolean;
}

interface ConfigureModalProps {
  id?: any;
  requirementId?: any;
  type?: string;
  isNodeType: string;
  closeModal: () => void;
  onSubmitSuccess: () => void;
  setLoadingAutoConfigure: (loading: boolean) => void;
  isCreateProject?: any;
  onClose?: () => void;
  setShowConfigModel?: any;
  patternType?: PatternType;
}

interface TreeNodeProps {
  option: ConfigOption;
  path: string;
  depth?: number;
}

interface ApiResponse {
  error?: string;
  task_id: string;
}
// Add mapping from config keys to timing keys
const CONFIG_KEY_TO_TIMING_KEY: Record<string, string> = {
    requirements: "requirements_autoconfig",
    epic: "epic_autoconfig",
    user_story: "user_story_autoconfig",
    testcase: "testcase_autoconfig",
    component_testcase: "component_testcase_autoconfig",
    architectural_requirements: "architectural_requirements_autoconfig",
    system_context: "system_context_autoconfig",
    container: "container_autoconfig",
    components: "components_autoconfig",
    interface: "interface_autoconfig",
    design: "design_autoconfig",
    documentation: "documentation_autoconfig",
    project: "project_autoconfig",
    // add more as needed
  };
  
const ConfigureModal: React.FC<ConfigureModalProps> = (props) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [allSelected, setAllSelected] = useState(false);
  const [config, setConfig] = useState<Record<string, ConfigOption> | null>(null);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [approvalOpen, setApprovalOpen] = useState(false);
  const [processing, setProcessing] = useState(false);
  const { setVerticalPanelState } = useContext(StateContext);
  // const { prepareDriver, handleDriveTour } = useContext(DriverContext);

  const modalRef = useRef<HTMLDivElement>(null);
  const { showAlert } = useContext(AlertContext);
  const { setCurrentTaskId, setIsNodeType, currentTaskId ,setConfiglabel} = useContext(ExecutionContext);
  const params = useParams();
  const projectId = params.projectId as string;

  useEffect(() => {
    setIsNodeType(props.isNodeType);
  }, [props.isNodeType, setIsNodeType]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        props.closeModal();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [props.closeModal]);

  useEffect(() => {
    const loadConfig = async () => {
      // Show loading state
      props.setLoadingAutoConfigure(true);
      
      try {
        // Fetch available configuration options
        const data = await getAvailableOptions(props.type || 'requirement');
        
        // Define keys to exclude from auto-selection
        const excludedKeys = ['task', 'testcase', 'documentation','user_story'];
        
        // Recursive function to set all configuration options to true, except excluded ones
        const setAllTrue = (obj :any) => {
          if (!obj || typeof obj !== 'object') return;
          
          Object.entries(obj).forEach(([key, value]) => {
            
            // Check if value is a configuration option object
            if (value && typeof value === 'object' && 'configure' in value) {
              // Set configure to false for excluded keys, true for others
              value.configure = !excludedKeys.includes(key);
              
              // Find nested configuration objects and process them recursively
              const nestedObj = Object.entries(value).reduce((acc:any, [k, v]) => {
                if (k !== 'configure' && v && typeof v === 'object' && 'configure' in v) {
                  acc[k] = v;
                }
                return acc;
              }, {});
              
              // Process nested configuration objects
              setAllTrue(nestedObj);
            }
          });
        };
        
        // Apply configuration settings
        setAllTrue(data);
        setAllSelected(true);
        
        // Update state with configured data
        setConfig(data);
      } catch (error) {
        
        // Handle error appropriately
      } finally {
        // Hide loading state
        props.setLoadingAutoConfigure(false);
      }
    };
    
    // Only load config if it hasn't been loaded yet
    if (!config) {
      loadConfig();
    }
  }, [config, props.type, props.setLoadingAutoConfigure, props.isCreateProject]);

  const isConfigOption = (value: unknown): value is ConfigOption => {
    return typeof value === 'object' && value !== null && 'configure' in value;
  };

  const toggleAllOptions = () => {
    const newState = !allSelected;
    setAllSelected(newState);

    const updateAllOptions = (obj: Record<string, ConfigOption>) => {
      Object.entries(obj).forEach(([key, value]) => {
        if (isConfigOption(value)) {
          value.configure = newState;
          const nestedObj = Object.entries(value).reduce((acc, [k, v]) => {
            if (k !== 'configure' && isConfigOption(v)) {
              acc[k] = v;
            }
            return acc;
          }, {} as Record<string, ConfigOption>);
          updateAllOptions(nestedObj);
        }
      });
    };

    if (config) {
      const updatedConfig = { ...config };
      updateAllOptions(updatedConfig);
      setConfig(updatedConfig);
    }
  };
// This is the updated handleToggle function that updates the allSelected state
const handleToggle = (path: string) => {
  if (!config) return;

  const keys = path.split('.');
  const newConfig = { ...config };

  const toggleState = (obj: Record<string, ConfigOption | boolean>, keys: string[]): void => {
    if (!obj || typeof obj !== 'object') return;

    if (keys.length === 1) {
      const currentObj = obj[keys[0]];
      if (isConfigOption(currentObj)) {
        const newValue = !currentObj.configure;
        currentObj.configure = newValue;

        // Update children
        const updateChildren = (node: Record<string, ConfigOption | boolean>) => {
          Object.entries(node).forEach(([key, value]) => {
            if (key !== 'configure' && isConfigOption(value)) {
              value.configure = newValue;
              updateChildren(value as Record<string, ConfigOption>);
            }
          });
        };
        updateChildren(currentObj);

        // Update parents if needed
        if (!newValue) {
          const updateParents = (currentPath: string[]) => {
            if (currentPath.length <= 1) return;
            const parentPath = currentPath.slice(0, -1);
            let current: ConfigOption | Record<string, ConfigOption> = newConfig;

            for (const key of parentPath) {
              const nextLevel: ConfigOption | undefined = (current as Record<string, ConfigOption>)[key];
              if (isConfigOption(nextLevel)) {
                current = nextLevel;
              } else {
                return;
              }
            }

            if (isConfigOption(current)) {
              current.configure = false;
            }
          };
          updateParents(keys);
        }
      }
    } else {
      const nextObj = obj[keys[0]];
      if (isConfigOption(nextObj)) {
        toggleState(nextObj, keys.slice(1));
      }
    }
  };

  toggleState(newConfig, keys);
  setConfig(newConfig);
  
  // After updating the configuration, check if all options are selected
  updateAllSelectedState(newConfig);
};

// Add this new function to check if all options are selected
const updateAllSelectedState = (configObj: Record<string, ConfigOption>) => {
  const checkAllSelected = (obj: Record<string, ConfigOption>): boolean => {
    return Object.entries(obj).every(([key, value]) => {
      if (isConfigOption(value)) {
        if (!value.configure) return false;
        
        // Check all nested options
        const nestedObj = Object.entries(value).reduce((acc, [k, v]) => {
          if (k !== 'configure' && isConfigOption(v)) {
            acc[k] = v;
          }
          return acc;
        }, {} as Record<string, ConfigOption>);
        
        return Object.keys(nestedObj).length === 0 || checkAllSelected(nestedObj);
      }
      return true;
    });
  };
  
  setAllSelected(checkAllSelected(configObj));
};

// Add this useEffect to initialize allSelected when config is loaded
useEffect(() => {
  if (config) {
    updateAllSelectedState(config);
  }
}, [config]);

  // Add a function to sum ETA based on selected items and pattern
  const getSelectedItemsETA = (
    configObj: Record<string, ConfigOption>,
    patternType: PatternType = PatternType.Monolithic
  ): number => {
    let total = 0;
    const timingMap: Record<string, number> =
      CONFIG_TIMING_BY_PATTERN[patternType];

    const sumRecursive = (obj: Record<string, ConfigOption>) => {
      Object.entries(obj).forEach(([key, value]) => {
        if (isConfigOption(value) && value.configure) {
          // Get nested configuration objects
          const nestedObj = Object.entries(value).reduce((acc, [k, v]) => {
            if (k !== "configure" && isConfigOption(v)) {
              acc[k] = v;
            }
            return acc;
          }, {} as Record<string, ConfigOption>);

          // If this item has no selected children, count it as a leaf
          const hasSelectedChildren = Object.values(nestedObj).some(
            (child) => isConfigOption(child) && child.configure
          );

          if (!hasSelectedChildren) {
            // Use the mapping to get the correct timing key
            const timingKey =
              CONFIG_KEY_TO_TIMING_KEY[key.toLowerCase()] || key.toLowerCase();
            if (timingMap && timingKey in timingMap) {
              // Return seconds directly
              total += timingMap[timingKey];
            } else {
              total += 5; // fallback (5 seconds)
            }
          } else {
            sumRecursive(nestedObj);
          }
        }
      });
    };

    sumRecursive(configObj);
    return total;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!config) return;

    const hasSelectedOptions = Object.values(config).some((item) => {
      if (typeof item === 'object') {
        return item.configure || Object.values(item).some(subItem =>
          typeof subItem === 'object' && subItem.configure
        );
      }
      return false;
    });

    // if (!hasSelectedOptions) {
    //   showAlert("Please select at least one option", "danger");
    //   return;
    // }

    if (config) (config as ConfigOption).configure = Boolean(true);

    setIsSubmitting(true);
    


    try {
      const response: ApiResponse = await configureNodeWithAgent({
        node_id: props.requirementId || props.id,
        node_type: props.isNodeType,
        user_level: 1,
        project_id: projectId,
        configurations: config,

      });

      if (response.error === "You already have a task in progress. Please cancel the current task to start a new one.") {
        showAlert(response.error, "danger");
        localStorage.setItem("current_task_id", response.task_id);
        setCurrentTaskId(response.task_id);
        setApprovalOpen(true);
        setConfiglabel("auto-config")
      } else {
        setCurrentTaskId(response.task_id);
        setConfiglabel("auto-config")
        props.closeModal();
        props.onSubmitSuccess();
        if (props.isCreateProject) {
          props.setShowConfigModel(true)
        }else {
          setVerticalPanelState('half');
        }
      }
    } catch (error) {
      showAlert("Error configuring node", "danger");
    } finally {
      setIsSubmitting(false);
    
    }
  };

  const CustomCheckbox: React.FC<{ checked: boolean; onChange: () => void }> = ({ checked, onChange }) => (
    <div
      onClick={onChange}
      className={`w-5 h-5 rounded-md border-2 flex items-center justify-center cursor-pointer transition-all duration-200 ${checked
        ? 'bg-blue-500 border-blue-500'
        : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50'
        }`}
    >
      {checked && <Check size={14} className="text-white" />}
    </div>
  );

  const renderOption = (option: ConfigOption, path: string, depth = 0) => {
    if (typeof option !== 'object' || option === null) return null;

    // Hide specific options from UI
    const hiddenOptions = ['task'];
    const currentKey = path.split('.').pop()?.toLowerCase();
    if (hiddenOptions.includes(currentKey || '')) {
      return null;
    }

    const isHovered = hoveredItem === path;
    const hasChildren = Object.entries(option).some(([key, value]) =>
      typeof value === 'object' && value !== null && key !== 'configure'
    );
    const displayName = path.split('.').pop()?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    return (
      <div key={path} className="flex flex-col">
        <div
          onMouseEnter={() => setHoveredItem(path)}
          onMouseLeave={() => setHoveredItem(null)}
          className={`flex items-center py-1.5 px-3 rounded-lg transition-all duration-200 ${isHovered ? 'bg-gray-50' : ''
            }`}
        >
          <div
            style={{ marginLeft: `${depth * 24}px` }}
            className="flex items-center flex-1 gap-3"
          >
            <CustomCheckbox
              checked={option.configure || false}
              onChange={() => handleToggle(path)}
            />
            <span className={`transition-colors duration-200 text-gray-700
              ${depth <= 1 ? 'typography-body' : 'typography-body-sm'}  
              ${depth === 0 ? 'font-weight-bold' : ''}
              ${depth === 1 ? 'font-weight-semibold' : ''}
              ${depth === 2 ? 'font-weight-semibold' : ''}
              ${depth === 3 ? 'font-weight-medium' : ''}
            `}>
              {displayName}
            </span>
            {hasChildren && (
              <ChevronRight size={16} className={`text-gray-400 transition-all duration-200 ${isHovered ? 'text-gray-600 transform translate-x-1' : ''
                }`} />
            )}
          </div>
        </div>
        {Object.entries(option).map(([key, value]) => {
          if (typeof value === 'object' && value !== null && key !== 'configure') {
            // Also filter out hidden options from children
            if (hiddenOptions.includes(key.toLowerCase())) {
              return null;
            }
            return renderOption(value as ConfigOption, `${path}.${key}`, depth + 1);
          }
          return null;
        })}
      </div>
    );
  };

  const TreeNode: React.FC<TreeNodeProps> = ({ option, path, depth = 0 }) => {
    if (typeof option !== 'object' || option === null) return null;

    const isHovered = hoveredItem === path;
    const hasChildren = Object.entries(option).some(
      ([key, value]) => typeof value === 'object' && value !== null && key !== 'configure'
    );
    const displayName = path.split('.').pop()?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    const childrenKeys = Object.keys(option).filter(key => key !== 'configure');

    return (
      <div className="relative">
        <div
          className={`flex items-start group ${depth > 0 ? 'ml-6' : ''}`}
          onMouseEnter={() => setHoveredItem(path)}
          onMouseLeave={() => setHoveredItem(null)}
        >
          {/* Vertical connector line from parent */}
          {depth > 0 && (
            <div className="absolute left-[-24px] top-0 bottom-0 w-px bg-gray-200 group-hover:bg-blue-200" />
          )}

          {/* Horizontal connector line to checkbox */}
          {depth > 0 && (
            <div className="absolute left-[-24px] top-1/2 w-6 h-px bg-gray-200 group-hover:bg-blue-200" />
          )}

          <div className={`relative flex items-center py-2 px-3 rounded-lg ${isHovered ? 'bg-gray-50' : ''
            }`}>
            <div className="flex items-center gap-3">
              <CustomCheckbox
                checked={option.configure || false}
                onChange={() => handleToggle(path)}
              />
              <span className={`transition-colors duration-200 text-gray-700
                ${depth === 0 ? 'typography-heading-5' : ''}
                ${depth === 1 ? 'typography-body-sm font-weight-semibold' : ''}
                ${depth >= 2 ? 'typography-body-sm' : ''}
              `}>
                {displayName}
              </span>
              {hasChildren && (
                <ChevronRight size={16} className={`text-gray-400 transition-transform duration-200 ${isHovered ? 'transform translate-x-1' : ''
                  }`} />
              )}
            </div>
          </div>
        </div>

        {/* Render children */}
        <div className="relative">
          {childrenKeys.map(key => {
            if (key === 'configure') return null;
            const childOption = option[key];
            if (typeof childOption === 'object' && childOption !== null) {
              return (
                <TreeNode
                  key={`${path}.${key}`}
                  option={childOption as ConfigOption}
                  path={`${path}.${key}`}
                  depth={depth + 1}
                />
              );
            }
            return null;
          })}
        </div>
      </div>
    );
  };

  // Helper to format ETA in seconds, mins or hours
  function formatETA(seconds: number): string {
    if (seconds < 60) {
      return `${Math.floor(seconds)} secs`;
    } else if (seconds < 3600) {
      const minutes = seconds / 60;
      return `${Math.floor(minutes)} mins`;
    } else {
      const hours = seconds / 3600;
      return `${Math.floor(hours)} hours`;
    }
  }

  // Helper to get display text for ETA range
  const getDisplayText = (
    configObj: Record<string, ConfigOption>,
    patternType: PatternType = PatternType.Monolithic
  ): string => {
    const minSeconds = getSelectedItemsETA(configObj, patternType);
    const maxSeconds = minSeconds * 2;

    // If both are under 60 seconds, show seconds
    if (minSeconds < 60 && maxSeconds < 60) {
      return `${Math.floor(minSeconds)}-${Math.floor(maxSeconds)} secs`;
    }
    // If both are under 3600 seconds (1 hour), show minutes
    else if (minSeconds < 3600 && maxSeconds < 3600) {
      const minMinutes = minSeconds / 60;
      const maxMinutes = maxSeconds / 60;
      return `${Math.floor(minMinutes)}-${Math.floor(maxMinutes)} mins`;
    }
    // If both are over 3600 seconds, show hours
    else {
      return `${formatETA(minSeconds)}-${formatETA(maxSeconds)}`;
    }
  };

  if (approvalOpen) {
    return (
      <EitherOrModal
        isOpen={approvalOpen}
        isProcessing={processing}
        onClose={() => setApprovalOpen(false)}
        type="task-approval"
        onAction={async (e: React.FormEvent) => {
          setProcessing(true);
          await deleteTask(currentTaskId, true);
          await handleSubmit(e);
          setProcessing(false);
          props.closeModal();
        }}
        buttons={[
          { name: 'Cancel', className: 'bg-gray-200 text-gray-800 hover:bg-gray-300' },
          { name: 'Overwrite Task', className: 'bg-red-600 text-white hover:bg-red-700' }
        ]}
      />
    );
  }

  if (!config) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center p-4 z-50 backdrop-blur-modal" style={{backgroundColor: 'hsl(var(--semantic-gray-900) / 0.15)'}}>
      <div ref={modalRef} className="bg-custom-bg-primary rounded-xl shadow-xl w-full max-w-md transform transition-all duration-200">
        <div className="px-6 py-4 border-b border-custom-border">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="typography-heading-4 font-weight-semibold text-custom-text-primary">Auto Configure</h2>
            </div>
            <button
              onClick={props.closeModal}
              className="text-semantic-gray-400 hover:text-semantic-gray-600 transition-colors p-1 hover:bg-semantic-gray-100 rounded-lg"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="px-4 py-4 max-h-[75vh] overflow-y-auto">
            <div className="flex justify-between mb-4">
              <p className="typography-body-sm text-gray-500 mt-1.5">Select items to configure</p>
              <button
                type="button"
                onClick={toggleAllOptions}
                className="typography-body-sm text-blue-600 hover:text-blue-700 font-weight-medium px-3 py-1.5 rounded-lg hover:bg-blue-50 transition-colors"
              >
                {allSelected ? 'Deselect All' : 'Select All'}
              </button>
            </div>
            <div className="space-y-1">
              {Object.entries(config).map(([key, value]) =>
                renderOption(value as ConfigOption, key)
              )}
            </div>
          </div>

          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-xl">
            {/* ETA Component */}
            {config && (
              <div className="mb-4">
                <ETAComponent
                  estimatedMinutes={
                    getSelectedItemsETA(
                      config,
                      (props.patternType as PatternType) || PatternType.Monolithic
                    ) / 60
                  }
                  displayText={getDisplayText(
                    config,
                    (props.patternType as PatternType) || PatternType.Monolithic
                  )}
                />
              </div>
            )}

            <div className="flex justify-end gap-3">
              <DynamicButton variant="secondary" onClick={props.closeModal}
                text="Cancel"
              />
              <DynamicButton
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                isLoading={isSubmitting}
                className=""
                text="Submit"
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ConfigureModal;